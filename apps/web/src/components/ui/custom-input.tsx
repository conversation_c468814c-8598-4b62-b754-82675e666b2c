import * as React from "react";
import { cn } from "@/lib/utils";

export interface CustomInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
}

const CustomInput = React.forwardRef<HTMLInputElement, CustomInputProps>(
  ({ className, type, label, placeholder, error, id, ...props }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const errorId = error ? `${inputId}-error` : undefined;

    return (
      <div className="flex flex-col gap-2 w-full">
        {label && (
          <label 
            htmlFor={inputId}
            className="font-['Roboto'] font-normal text-[16px] text-[#0c1421] tracking-[0.16px]"
          >
            {label}
          </label>
        )}
        <div className="relative w-full">
          <input
            id={inputId}
            type={type}
            aria-describedby={errorId}
            aria-invalid={error ? true : undefined}
            className={cn(
              "w-full lg:w-[388px] h-12 bg-[#f7fbff] border border-[#d4d7e3] rounded-xl px-4 py-3",
              "font-['Roboto'] font-normal text-[16px] tracking-[0.16px]",
              "placeholder:text-[#8897ad] placeholder:font-['Roboto'] placeholder:font-normal",
              "focus:outline-none focus:ring-2 focus:ring-[#e7542a] focus:border-transparent",
              "disabled:opacity-50 disabled:cursor-not-allowed",
              error && "border-red-500 focus:ring-red-500",
              className
            )}
            placeholder={placeholder}
            ref={ref}
            {...props}
          />
        </div>
        {error && (
          <p id={errorId} className="text-red-500 text-sm" role="alert">
            {error}
          </p>
        )}
      </div>
    );
  }
);
CustomInput.displayName = "CustomInput";

export { CustomInput };