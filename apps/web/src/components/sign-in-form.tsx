import { authClient } from "@/lib/auth-client";
import { useForm } from "@tanstack/react-form";
import { toast } from "sonner";
import z from "zod";
import Loader from "./loader";
import { CustomButton } from "./ui/custom-button";
import { CustomInput } from "./ui/custom-input";
import { useRouter } from "next/navigation";

export default function SignInForm() {
  const router = useRouter()
  const { isPending } = authClient.useSession();

  const form = useForm({
    defaultValues: {
      email: "",
      password: "",
    },
    onSubmit: async ({ value }) => {
      await authClient.signIn.email(
        {
          email: value.email,
          password: value.password,
        },
        {
          onSuccess: () => {
            router.push("/dashboard")
            toast.success("Sign in successful");
          },
          onError: (error) => {
            toast.error(error.error.message || error.error.statusText);
          },
        },
      );
    },
    validators: {
      onSubmit: z.object({
        email: z.email("Invalid email address"),
        password: z.string().min(8, "Password must be at least 8 characters"),
      }),
    },
  });

  if (isPending) {
    return <Loader />;
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen lg:h-[778px] w-full max-w-[702px] px-6 lg:px-[120px] py-8 lg:py-2.5">
      <div className="flex flex-col gap-8 lg:gap-12 lg:h-[665px] items-center justify-center w-full">
        {/* Header Section */}
        <div className="flex flex-col gap-5 lg:gap-7 items-start justify-start text-center w-full">
          <div className="flex flex-col justify-center w-full">
            <h1 className="font-['SF_Pro_Rounded'] text-[28px] lg:text-[36px] text-[#0c1421] tracking-[0.36px] leading-none">
              <span className="font-semibold">Welcome Back </span>
              <span className="font-normal">👋</span>
            </h1>
          </div>
          <div className="flex flex-col justify-center w-full">
            <p className="font-['SF_Pro_Rounded'] font-normal text-[18px] lg:text-[20px] text-[#313957] tracking-[0.2px] leading-[1.6]">
              Log in to manage and monitor your patients with ease.
            </p>
          </div>
        </div>

        {/* Form Section */}
        <div className="flex flex-col gap-6 items-end justify-center w-full">
          <form
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
              form.handleSubmit();
            }}
            className="flex flex-col gap-6 w-full"
            aria-label="Sign in form"
          >
            {/* Email Field */}
            <form.Field name="email">
              {(field) => (
                <div className="flex flex-col gap-2 w-full">
                  <CustomInput
                    id={field.name}
                    name={field.name}
                    type="email"
                    label="Email"
                    placeholder="<EMAIL>"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p key={error?.message} className="text-red-500 text-sm">
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>

            {/* Password Field */}
            <form.Field name="password">
              {(field) => (
                <div className="flex flex-col gap-2 w-full">
                  <CustomInput
                    id={field.name}
                    name={field.name}
                    type="password"
                    label="Password"
                    placeholder="At least 8 characters"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p key={error?.message} className="text-red-500 text-sm">
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>

            {/* Forgot Password Link */}
            <div className="flex justify-end">
              <button
                type="button"
                className="font-['Roboto'] font-normal text-[16px] text-[#1e4ae9] tracking-[0.16px] hover:underline focus:outline-none focus:ring-2 focus:ring-[#1e4ae9] rounded"
                aria-label="Forgot password? Reset your password"
              >
                Forgot Password?
              </button>
            </div>

            {/* Sign In Button */}
            <form.Subscribe>
              {(state) => (
                <CustomButton
                  type="submit"
                  disabled={!state.canSubmit || state.isSubmitting}
                >
                  {state.isSubmitting ? "Submitting..." : "Sign in"}
                </CustomButton>
              )}
            </form.Subscribe>
          </form>
        </div>

        {/* Social Sign In Section */}
        <div className="flex flex-col gap-6 items-start justify-start w-full">
          {/* Divider */}
          <div className="flex gap-4 items-center justify-center px-0 py-2.5 w-full">
            <div className="flex-1 h-px bg-[#cfdfe2]" />
            <div className="font-['Roboto'] font-normal text-[16px] text-[#294957] tracking-[0.16px] text-center">
              Or
            </div>
            <div className="flex-1 h-px bg-[#cfdfe2]" />
          </div>

          {/* Google Sign In Button */}
          <div className="flex flex-col gap-4 w-full">
            <button 
              type="button"
              className="bg-[#f3f9fa] flex gap-4 items-center justify-center px-[9px] py-3 rounded-xl w-full hover:bg-[#e8f4f6] transition-colors focus:outline-none focus:ring-2 focus:ring-[#e7542a]"
              aria-label="Sign in with Google"
            >
              <div className="w-7 h-7">
                <img 
                  src="http://localhost:3845/assets/1ebfc59ebadb322bdd4524cd2f99832067d96af4.svg" 
                  alt="Google logo" 
                  className="w-full h-full"
                />
              </div>
              <div className="font-['Roboto'] font-normal text-[16px] text-[#313957] tracking-[0.16px]">
                Sign in with Google
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8 lg:absolute lg:bottom-2 font-['Roboto'] font-normal text-[16px] text-[#8897ad] text-center tracking-[0.16px]">
        © 2025 ALL RIGHTS RESERVED
      </div>
    </div>
  );
}
