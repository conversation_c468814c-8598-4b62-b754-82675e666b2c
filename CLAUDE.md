# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a monorepo built with Better T3 Stack using Turborepo for orchestration. It includes:
- **Web app** (Next.js) - Frontend at port 3001
- **Server** (Next.js + tRPC) - Backend API at port 3000  
- **Native app** (React Native + Expo) - Mobile application

## Common Development Commands

### Starting Development
```bash
# Start all services
pnpm dev

# Start individual services
pnpm dev:web      # Web frontend only
pnpm dev:server   # Backend API only
pnpm dev:native   # Mobile app only
```

### Building and Type Checking
```bash
pnpm build         # Build all applications
pnpm check-types   # TypeScript type checking across all apps
```

### Database Operations (Drizzle ORM)
```bash
pnpm db:push      # Push schema changes to database (development)
pnpm db:generate  # Generate migration files
pnpm db:migrate   # Run migrations (production)
pnpm db:studio    # Open Drizzle Studio (database GUI)
```

### Package Management
Use pnpm workspaces. Navigate to specific app directories to add dependencies:
```bash
cd apps/web && pnpm add package-name      # Web-specific dependency
cd apps/server && pnpm add package-name   # Server-specific dependency  
cd apps/native && pnpm add package-name   # Native-specific dependency
```

## Architecture Overview

### Technology Stack
- **Frontend**: Next.js 15 with App Router, Tailwind CSS, shadcn/ui
- **Backend**: Next.js API routes with tRPC for type-safe APIs
- **Database**: Neon PostgreSQL with Drizzle ORM
- **Authentication**: Better Auth (email/password)
- **Mobile**: React Native with Expo, NativeWind for styling
- **Build System**: Turborepo with pnpm workspaces

### Key Directories

#### Server (`apps/server/`)
- `src/db/schema/` - Drizzle database schemas (auth.ts, todo.ts)
- `src/routers/` - tRPC API route definitions
- `src/lib/` - Shared server utilities (auth, context, trpc setup)
- `drizzle.config.ts` - Drizzle configuration

#### Web (`apps/web/`)
- `src/app/` - Next.js App Router pages and layouts
- `src/components/ui/` - shadcn/ui components
- `src/lib/` - Client utilities and tRPC client setup
- `src/utils/trpc.ts` - tRPC client configuration

#### Native (`apps/native/`)
- `app/` - Expo Router file-based routing
- `components/` - Shared React Native components
- `lib/` - Native utilities and tRPC client setup

### Database Schema
Currently includes:
- **User authentication** tables (user, session, account, verification)
- **Todo** table for the sample application

### tRPC API Structure
- Main router in `apps/server/src/routers/index.ts`
- Todo router with CRUD operations
- Protected and public procedures available
- Type safety maintained across all three platforms

## Development Patterns

### Adding New Features
1. Define database schema in `apps/server/src/db/schema/`
2. Create tRPC router in `apps/server/src/routers/`
3. Add router to main app router
4. Implement UI in web and/or native apps using tRPC client
5. Use `pnpm db:push` to sync schema changes during development

### Authentication Flow
- Better Auth handles sessions across web and mobile
- Use `authClient` for authentication operations
- Protected procedures in tRPC check authentication automatically

### Styling
- **Web**: Tailwind CSS with shadcn/ui components
- **Native**: NativeWind (Tailwind for React Native)

## Environment Setup
- Server requires `.env` file with DATABASE_URL and authentication secrets
- Web app needs NEXT_PUBLIC_SERVER_URL pointing to server (typically http://localhost:3000)
- Native app shares authentication configuration with web